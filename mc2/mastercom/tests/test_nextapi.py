# base test class
from django.test import TestCase
from unittest.mock import patch, Mock

from mc2.mastercom.next_api import NextApi


class NextApiTest(TestCase):

    def test_set_token(self):
        next_api = NextApi()
        next_api.set_token('test')
        self.assertIsNotNone(next_api.token)

    @patch('requests.get', return_value=Mock(status_code=200, json=Mock(return_value={'type': {'mc2': 4}, 'usr': 'test', 'couch_id': 'test'})))
    def test_verify_ok(self, mock):
        next_api = NextApi()
        user_data = next_api.verify('ok_tk')

        self.assertEqual(user_data['type'], {'mc2': 4})
        self.assertEqual(user_data['usr'], 'test')
        self.assertEqual(user_data['couch_id'], 'test')

    @patch('requests.get', return_value=Mock(status_code=401))
    def test_verify_bad_token(self, mock):
        next_api = NextApi()
        self.assertFalse(next_api.verify('bad_tk'))
