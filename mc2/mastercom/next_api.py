import requests

from django.conf import settings


class NextApi:
    url = None
    token = None

    def __init__(self):
        self.url = settings.NEXT_API_URL or ''

    def set_token(self, token):
        self.token = token

    def get(self, endpoint, params=None):
        if not self.token:
            raise Exception('Token is mandatory')
        res = requests.get(self.url + endpoint, params=params, headers={'Authorization': self.token})
        if res.status_code != 200:
            raise Exception('Error calling NextApi: ' + res.text)
        return res.json()

    def login(self, username: str, password: str):
        res = requests.post(self.url + '/next-api/v1/login', json={'username': username, 'password': password})
        if res.status_code != 200:
            raise Exception('Error calling NextApi login: ' + res.text)
        self.token = res.text
        return True

    def verify(self, token: str) -> any:
        headers = {
            "Authorization": token,
            "Content-Type": "application/json"
        }

        url = self.url + '/next-api/v1/login'
        print(f"NextApi.verify() - URL: {url}")
        print(f"NextApi.verify() - Headers: {headers}")

        try:
            res = requests.get(url, headers=headers)
            print(f"NextApi.verify() - Status Code: {res.status_code}")
            print(f"NextApi.verify() - Response Text: {res.text}")

            if res.status_code != 200:
                return False
            return res.json()
        except Exception as e:
            print(f"NextApi.verify() - Errore nella richiesta: {e}")
            return False