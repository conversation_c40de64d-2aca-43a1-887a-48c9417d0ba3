from django.test import TestCase
from django.urls import reverse
from ..factories import MovementFactory, MovementTypeFactory, CategoryFactory, AeCategoryFactory

from django.contrib.auth import get_user_model
User = get_user_model()


class TestMovementAdmin(TestCase):

    def setUp(self):
        self.list = reverse('admin:cashflow_ccpmovement_changelist')
        User.objects.create_user(username='admin', password='admin', is_superuser=True, is_staff=True)

    def test_list_unauthorized(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 302)

    def test_list_authorized(self):
        self.assertTrue(self.client.login(username='admin', password='admin'))
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_list_display(self):
        MovementFactory(expiration_date=1672828800)
        self.assertTrue(self.client.login(username='admin', password='admin'))
        response = self.client.get(self.list)
        self.assertContains(response, '2023')
        self.assertContains(response, '4')

    def test_change_display(self):
        movement = MovementFactory(expiration_date=1672828800)
        change = reverse('admin:cashflow_ccpmovement_change', args=[movement.pk])
        self.assertTrue(self.client.login(username='admin', password='admin'))
        response = self.client.get(change)
        self.assertContains(response, '2023')
        self.assertContains(response, '04')
        self.assertContains(response, '01')
        self.assertNotContains(response, '1672828800')
        self.assertContains(response, '4')

class TestMovementTypeAdmin(TestCase):

    def setUp(self):
        self.list = reverse('admin:cashflow_ccptype_changelist')
        User.objects.create_user(username='admin', password='admin', is_superuser=True, is_staff=True)

    def test_list_unauthorized(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 302)

    def test_list_authorized(self):
        self.assertTrue(self.client.login(username='admin', password='admin'))
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)

    def test_list_display(self):
        # 'id', 'category', 'school_year', 'incoming', 'ccp_ae_category')
        MovementTypeFactory(
            school_year='2024/2025',
            category=CategoryFactory(name='Test Category'),
            incoming=True,
            ccp_ae_category=AeCategoryFactory(description='Test Ae Category')
            )
        self.assertTrue(self.client.login(username='admin', password='admin'))
        response = self.client.get(self.list)
        self.assertContains(response, '2024/2025')
        self.assertContains(response, 'Test Category')
        self.assertContains(response, 'Test Ae Category')


class TestCategoryAdmin(TestCase):

    def setUp(self):
        self.list = reverse('admin:cashflow_ccpcategory_changelist')
        User.objects.create_user(username='admin', password='admin', is_superuser=True, is_staff=True)

    def test_list_unauthorized(self):
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 302)

    def test_list_authorized(self):
        self.assertTrue(self.client.login(username='admin', password='admin'))
        response = self.client.get(self.list)
        self.assertEqual(response.status_code, 200)
