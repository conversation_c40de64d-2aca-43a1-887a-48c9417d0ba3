from datetime import datetime

from django.test import TestCase
from django.urls import reverse

from ..factories import MovementFactory, MovementTypeFactory
from ...admin.forms import MovementForm as AdminMovementForm

from django.contrib.auth import get_user_model
User = get_user_model()

class TestMovementAdminForm(TestCase):

    def test_form_expiration_date_auto_population(self):
        movement = MovementFactory()
        form = AdminMovementForm(instance=movement)

        self.assertEqual(form.instance, movement)
        self.assertTrue('expiration_date' in form.fields)
        self.assertEqual(form.initial['expiration_date'], datetime.fromtimestamp(movement.expiration_date).date())

    def test_expiration_date_edit(self):
        expiration_date = datetime.strptime('2021-01-01', '%Y-%m-%d')
        expiration_date_int = int(expiration_date.timestamp())
        movement = MovementFactory(expiration_date=1)
        form = AdminMovementForm(data=dict(
            expiration_date=expiration_date.date(),
            creation_date=expiration_date.date(),
            amount=1000,
            subject_school_year=2021,
            subject_type='S',
            description='description',
            type=MovementTypeFactory(),
        ), instance=movement)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['expiration_date'], expiration_date_int)

    def test_expiration_date_create(self):
        expiration_date = datetime.strptime('2021-01-01', '%Y-%m-%d')
        expiration_date_int = int(expiration_date.timestamp())
        form = AdminMovementForm(data=dict(
            expiration_date=expiration_date.date(),
            creation_date=expiration_date.date(),
            amount=1000,
            subject_school_year=2021,
            subject_type='S',
            description='description',
            type=MovementTypeFactory(),
        ))

        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['expiration_date'], expiration_date_int)
