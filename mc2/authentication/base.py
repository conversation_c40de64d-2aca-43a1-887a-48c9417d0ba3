from django.conf import settings

from rest_framework.authentication import BaseAuthentication
from rest_framework import permissions

from mc2.mastercom.next_api import NextApi
from .utils import handle_user_from_next_api

class NextApiAuthentication(BaseAuthentication):

    def authenticate(self, request):
        # Debug completo degli header
        print("=== DEBUG HEADERS ===")
        print(f"request.META keys con 'AUTH': {[k for k in request.META.keys() if 'AUTH' in k]}")
        print(f"HTTP_AUTHORIZATION: {repr(request.META.get('HTTP_AUTHORIZATION'))}")
        print(f"Authorization header: {repr(request.headers.get('Authorization'))}")
        print(f"authorization header: {repr(request.headers.get('authorization'))}")

        # Prova entrambi i metodi
        token_meta = request.META.get('HTTP_AUTHORIZATION')
        token_headers = request.headers.get('Authorization')

        print(f"Token da META: {repr(token_meta)}")
        print(f"Token da headers: {repr(token_headers)}")

        # Usa il token che non è None
        token = token_headers if token_headers else token_meta
        print(f"Token scelto: {repr(token)}")

        if not token:
            return None, None

        # Rimuovi eventuali virgolette all'inizio e alla fine del token
        token_cleaned = token.strip('"\'')
        print(f"Token dopo strip: {repr(token_cleaned)}")

        # Rimuovi il prefisso "Bearer " se presente
        if token_cleaned.startswith('Bearer '):
            token_cleaned = token_cleaned[7:]
            print(f"Token dopo rimozione Bearer: {repr(token_cleaned)}")

        print(f"Token finale: {repr(token_cleaned)}")
        token = token_cleaned
        next_api = NextApi()
        user_data = next_api.verify(token)
        if user_data:
            return (handle_user_from_next_api(user_data), token)
        return None, token


class IsLocalhost(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.META['REMOTE_ADDR'] in settings.AUTH_ALLOWED_HOSTS