from django.conf import settings

from rest_framework.authentication import TokenAuthentication
from rest_framework import permissions

from mc2.mastercom.next_api import NextApi
from .utils import handle_user_from_next_api

class NextApiAuthentication(TokenAuthentication):

    def authenticate(self, request):
        token = request.headers.get('Authorization', '')
        token = token.replace('Bearer ', '')
        if not token:
            return None, None

        next_api = NextApi()
        user_data = next_api.verify(token)
        if user_data:
            return (handle_user_from_next_api(user_data), token)
        return None, token


class IsLocalhost(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.META['REMOTE_ADDR'] in settings.AUTH_ALLOWED_HOSTS